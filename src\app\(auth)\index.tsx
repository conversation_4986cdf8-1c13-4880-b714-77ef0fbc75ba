import imagePath from '@/src/constants/imagePath'
import React from 'react'
import { StyleSheet, Text, View,Image } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const Authindex = () => {
  return (
    <SafeAreaView style={styles.container} >
      
      <View style={styles.header}></View>
      <View style={styles.body}>
        <Image source={imagePath.logo}/>
      </View>
      <View style={styles.footer}>
        <Text style={styles.from_text}>From</Text>
        <Text style={styles.facebook_text}>Facebook</Text>
      </View>

    </SafeAreaView>
  )
}

export default Authindex

const styles= StyleSheet.create({
    container:{
        flex:1,
        backgroundColor:'white',
        justifyContent:'space-between',
        alignItems:'center',
        paddingVertical:70
    },
    header:{
        
    },
    body:{
        
    },
    footer:{
        alignItems:"center"
    },
    from_text:{
      fontSize:12,
      color:'gray'
    },
    facebook_text:{
      fontSize:15,
      color:"black"
    }

})